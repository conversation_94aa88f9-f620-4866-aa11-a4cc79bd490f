#!/usr/bin/env python3
"""
IDE Reset Tool - Compact Version
Lightweight cross-IDE reset utility for Windows.
"""

import os, json, sqlite3, uuid, shutil, hashlib, time, psutil, random, glob
from datetime import datetime
from typing import Dict, List, Optional, Any, cast

# Color support
try:
    from colorama import init, Fore, Style
    init(autoreset=True)
except ImportError:
    class _ColorFallback:
        def __getattr__(self, _name: str) -> str: return ''
    Fore = Style = _ColorFallback()

# Database cleaning patterns (comprehensive like windows.ps1)
DB_PATTERNS = [
    # Augment-related entries
    "%augment%", "Augment.%", "augment.%", "%augment-chat%", "%augment-panel%",
    "%augment-view%", "%augment-extension%", "%vscode-augment%", "%augmentcode%",
    "%augment.code%", "%memento/webviewView.augment%", "%workbench.view.extension.augment%",
    "%workbench.panel.augment%", "%extensionHost.augment%",

    # Telemetry and tracking entries
    "%telemetry%", "%machineId%", "%deviceId%", "%sqmId%", "%machine-id%",
    "%device-id%", "%sqm-id%", "%sessionId%", "%session-id%", "%userId%",
    "%user-id%", "%installationId%", "%installation-id%",

    # Context7 and trial-related entries
    "%context7%", "%trial%", "%trialPrompt%", "%trial-prompt%",
    "%licenseCheck%", "%license-check%",

    # Extension tracking and analytics
    "%extensionTelemetry%", "%extension-telemetry%", "%analytics%",
    "%tracking%", "%metrics%", "%usage%", "%statistics%",

    # AI and ML service identifiers
    "%aiService%", "%ai-service%", "%mlService%", "%ml-service%",
    "%copilot%", "%github.copilot%",

    # Authentication and session tokens
    "%authToken%", "%auth-token%", "%accessToken%", "%access-token%",
    "%refreshToken%", "%refresh-token%", "secret://%", "%session%",

    # Specific telemetry entries
    "workbench.telemetryOptOutShown", "telemetry.firstSessionDate",
    "telemetry.lastSessionDate", "telemetry.currentSessionDate",
    "storage.serviceMachineId"
]

# IDE definitions
IDE_DEFS = {
    'vscode': {'name': 'VSCode', 'app_folder': 'Code', 'process': 'Code.exe'},
    'cursor': {'name': 'Cursor', 'app_folder': 'Cursor', 'process': 'Cursor.exe'}
}

audit_log_file = None

def write_audit_log(action: str, details: str) -> None:
    """Write audit log entry."""
    global audit_log_file
    if not audit_log_file:
        os.makedirs("logs", exist_ok=True)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        audit_log_file = f"logs/ide_reset_audit_{timestamp}.log"

    entry = f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] [AUDIT] {action} - {details}"
    print(f"{Fore.MAGENTA}{entry}{Style.RESET_ALL}")
    try:
        with open(audit_log_file, 'a', encoding='utf-8') as f:
            f.write(entry + '\n')
    except: pass

class OperationTimer:
    def __init__(self, timeout_seconds: int = 1800):
        self.start_time = datetime.now()
        self.timeout_seconds = timeout_seconds
        self.operation_name = "Unknown"

    def set_operation(self, name: str) -> None:
        self.operation_name = name

    def check_timeout(self, stage: str = "") -> bool:
        elapsed = (datetime.now() - self.start_time).total_seconds()
        if elapsed > self.timeout_seconds:
            print(f"{Fore.RED}❌ Timeout exceeded ({elapsed:.1f}s){Style.RESET_ALL}")
            write_audit_log("TIMEOUT_ERROR", f"{self.operation_name} - {stage}")
            return True
        return False

    def get_elapsed_time(self) -> float:
        return (datetime.now() - self.start_time).total_seconds()

def detect_running_processes(process_names: List[str]) -> Dict[str, List[Dict[str, Any]]]:
    """Detect running processes by name."""
    running_processes: Dict[str, List[Dict[str, Any]]] = {}
    try:
        # Type annotation for psutil.process_iter
        process_attrs = ['pid', 'name', 'create_time']
        for proc in psutil.process_iter(process_attrs):  # type: ignore[misc]
            proc_info = proc.info
            proc_name = proc_info.get('name', '').lower()
            for target_name in process_names:
                if target_name.lower() in proc_name:
                    if target_name not in running_processes:
                        running_processes[target_name] = []
                    try:
                        create_time = datetime.fromtimestamp(proc_info.get('create_time', 0))
                        running_time = datetime.now() - create_time
                        running_processes[target_name].append({
                            'pid': proc_info.get('pid'),
                            'running_time': running_time,
                            'process_obj': proc
                        })
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        continue
    except Exception as e:
        write_audit_log("PROCESS_DETECTION_ERROR", f"Failed to detect processes: {e}")
    return running_processes

def interactive_process_handling(ide_name: str, process_name: str, timer: OperationTimer) -> bool:
    """Handle running IDE processes."""
    if timer.check_timeout("process detection"):
        return False

    running_procs = detect_running_processes([process_name])
    if not running_procs.get(process_name):
        print(f"{Fore.GREEN}✓ {ide_name} not running{Style.RESET_ALL}")
        return True

    processes = running_procs[process_name]
    print(f"{Fore.YELLOW}⚠️  Found {len(processes)} running {ide_name} process(es):{Style.RESET_ALL}")
    for i, proc_info in enumerate(processes, 1):
        running_time = str(proc_info['running_time']).split('.')[0]
        print(f"  {i}. PID: {proc_info['pid']}, Running: {running_time}")

    print(f"\n{Fore.CYAN}Options:{Style.RESET_ALL}")
    print("1. Close all processes automatically")
    print("2. Continue anyway (may cause file lock issues)")
    print("3. Cancel operation")

    while True:
        choice = input(f"\nChoice (1-3): ").strip()
        if choice == '1':
            return close_ide_processes(ide_name, processes, timer)
        elif choice == '2':
            print(f"{Fore.YELLOW}⚠️  Continuing with {ide_name} running.{Style.RESET_ALL}")
            return True
        elif choice == '3':
            print(f"{Fore.YELLOW}Operation cancelled{Style.RESET_ALL}")
            return False
        else:
            print(f"{Fore.RED}Invalid choice. Please enter 1, 2, or 3.{Style.RESET_ALL}")

def close_ide_processes(ide_name: str, processes: List[Dict[str, Any]], _timer: OperationTimer) -> bool:
    """Close IDE processes."""
    print(f"Closing {len(processes)} {ide_name} process(es)...")
    write_audit_log("PROCESS_CLOSE_START", f"{ide_name} - Attempting to close {len(processes)} processes")

    for proc_info in processes:
        try:
            proc = proc_info['process_obj']
            proc.terminate()
            print(f"  → Sent terminate signal to PID {proc_info['pid']}")
        except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
            print(f"  → Failed to terminate PID {proc_info['pid']}: {e}")

    print("  → Waiting for processes to close...")
    time.sleep(3)

    # Force kill if needed
    for proc_info in processes:
        try:
            proc = proc_info['process_obj']
            if proc.is_running():
                proc.kill()
                print(f"  → Force killed PID {proc_info['pid']}")
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue

    write_audit_log("PROCESS_CLOSE_SUCCESS", f"{ide_name} - All processes closed successfully")
    print(f"{Fore.GREEN}✓ All {ide_name} processes closed successfully{Style.RESET_ALL}")
    return True

def discover_ides() -> Dict[str, Dict[str, str]]:
    """Discover installed IDEs from Windows Registry."""
    print("Detecting installed IDEs...")
    discovered: Dict[str, Dict[str, str]] = {}

    try:
        import winreg
        registry_paths = [
            (winreg.HKEY_CURRENT_USER, r'SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall'),
            (winreg.HKEY_LOCAL_MACHINE, r'SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall'),
        ]

        for hive, path in registry_paths:
            try:
                with winreg.OpenKey(hive, path) as key:
                    i = 0
                    while True:
                        try:
                            subkey_name = winreg.EnumKey(key, i)
                            with winreg.OpenKey(key, subkey_name) as subkey:
                                try:
                                    display_name = winreg.QueryValueEx(subkey, 'DisplayName')[0].lower()
                                    ide_key = None
                                    if 'cursor' in display_name:
                                        ide_key = 'cursor'
                                    elif 'visual studio code' in display_name and 'insiders' not in display_name:
                                        ide_key = 'vscode'

                                    if ide_key and ide_key not in discovered:
                                        props = IDE_DEFS[ide_key]
                                        appdata_path = os.getenv('APPDATA')
                                        if appdata_path:
                                            app_path = os.path.join(appdata_path, props['app_folder'])
                                            if os.path.isdir(app_path):
                                                discovered[props['name']] = {
                                                    "app_path": app_path,
                                                    "process": props['process'],
                                                }
                                except FileNotFoundError:
                                    pass
                        except OSError:
                            break
                        i += 1
            except FileNotFoundError:
                pass
    except ImportError:
        print(f"{Fore.YELLOW}Could not import 'winreg'.{Style.RESET_ALL}")

    if discovered:
        print(f"{Fore.GREEN}✓ Found: {', '.join(discovered.keys())}{Style.RESET_ALL}")
    else:
        print(f"{Fore.YELLOW}No supported IDEs found.{Style.RESET_ALL}")
    return discovered

def get_config(base_config: Dict[str, str]) -> Dict[str, Any]:
    """Get full configuration paths for an IDE."""
    config: Dict[str, Any] = base_config.copy()
    user_path = os.path.join(config['app_path'], 'User')
    config.update({
        'user_path': user_path,
        'storage_json': os.path.join(user_path, 'globalStorage', 'storage.json'),
        'state_db': os.path.join(user_path, 'globalStorage', 'state.vscdb'),
        'machine_id_files': [
            os.path.join(config['app_path'], 'machineid'),           # Root machineid
            os.path.join(user_path, 'machineid')                     # User machineid
        ],
        'workspace_storage': os.path.join(user_path, 'workspaceStorage'),
        'cache_dirs': [os.path.join(config['app_path'], d) for d in
                      ['Cache', 'Code Cache', 'GPUCache', 'CachedData', 'CachedExtensionVSIXs']]
    })
    return config

def discover_database_files(config: Dict[str, Any]) -> List[str]:
    """Discover all database files."""
    print(f"  -> Discovering database files (comprehensive scan)...")
    db_files: List[str] = []
    base_path: str = config['app_path']

    search_patterns = [
        "User/workspaceStorage/*/state.vscdb",
        "User/globalStorage/state.vscdb"
    ]

    for pattern in search_patterns:
        pattern_path = os.path.join(base_path, pattern.replace('/', os.sep))
        if '*' in pattern_path:
            found_files = glob.glob(pattern_path)
            for file_path in found_files:
                if os.path.isfile(file_path):
                    db_files.append(file_path)
                    print(f"     -> Found database file: {os.path.relpath(file_path, base_path)}")
        else:
            if os.path.isfile(pattern_path):
                db_files.append(pattern_path)
                print(f"     -> Found database file: {os.path.relpath(pattern_path, base_path)}")

    print(f"  -> Discovered {len(db_files)} database files")
    return db_files

def is_ide_running(process_name: str) -> bool:
    """Check if IDE is running."""
    try:
        return any(p.info.get('name') == process_name for p in psutil.process_iter(['name']))  # type: ignore[misc]
    except:
        return False

# --- Reset Functions ---

def reset_machine_id(config: Dict[str, Any], dry_run: bool = False) -> None:
    """Reset all tracking identifiers."""
    print(f"{Fore.CYAN}--- Resetting Machine IDs ---{Style.RESET_ALL}")

    if dry_run:
        print("  -> [Dry Run] Would generate new tracking identifiers")
        print(f"  -> Target files:")
        print(f"     - {config['storage_json']}")
        print(f"     - {config['state_db']}")
        for machine_file in config['machine_id_files']:
            print(f"     - {machine_file}")
        return

    # Generate new IDs
    current_time = int(time.time() * 1000)
    past_time = current_time - random.randint(86400000, 2592000000)  # 1-30 days ago

    new_ids: Dict[str, str] = {
        'telemetry.machineId': hashlib.sha256(uuid.uuid4().bytes).hexdigest(),
        'telemetry.devDeviceId': str(uuid.uuid4()),
        'telemetry.sqmId': f"{{{str(uuid.uuid4()).upper()}}}",
        'storage.serviceMachineId': hashlib.sha256(uuid.uuid4().bytes).hexdigest(),
        'telemetry.sessionId': str(uuid.uuid4()),
        'telemetry.userId': str(uuid.uuid4()),
        'telemetry.installationId': str(uuid.uuid4()),
        'telemetry.firstSessionDate': str(past_time),
        'telemetry.lastSessionDate': str(current_time),
        'telemetry.currentSessionDate': str(current_time),
    }
    
    print(f"  -> Generated new secure identifiers")

    # Update storage.json
    storage_file: str = config['storage_json']
    print(f"  -> Processing: {storage_file}")
    if os.path.exists(storage_file):
        try:
            with open(storage_file, 'r+', encoding='utf-8') as f:
                data = json.load(f)
                updated: List[str] = []
                for k, v in new_ids.items():
                    if k.startswith('telemetry.') and k in data:
                        old_value = data[k]
                        data[k] = v
                        updated.append(f"{k}: {old_value[:8]}... -> {v[:8]}...")

                if updated:
                    f.seek(0)
                    json.dump(data, f, indent=2)
                    f.truncate()
                    print(f"{Fore.GREEN}✓ Updated storage.json with {len(updated)} IDs:{Style.RESET_ALL}")
                    for update in updated:
                        print(f"     - {update}")
                else:
                    print(f"{Fore.YELLOW}  -> No telemetry keys found in storage.json{Style.RESET_ALL}")
        except Exception as e:
            print(f"{Fore.RED}  -> Error updating {storage_file}: {e}{Style.RESET_ALL}")
    else:
        print(f"{Fore.YELLOW}  -> File not found: {storage_file}{Style.RESET_ALL}")

    # Update state database
    db_file: str = config['state_db']
    print(f"  -> Processing: {db_file}")
    if os.path.exists(db_file):
        try:
            with sqlite3.connect(db_file) as conn:
                cursor = conn.cursor()
                updated: List[str] = []
                for k, v in new_ids.items():
                    # Check if key exists first
                    cursor.execute("SELECT value FROM ItemTable WHERE key = ?", (k,))
                    old = cursor.fetchone()
                    old_val = old[0] if old else "NEW"

                    cursor.execute("INSERT OR REPLACE INTO ItemTable (key, value) VALUES (?, ?)", (k, v))
                    updated.append(f"{k}: {str(old_val)[:8]}... -> {str(v)[:8]}...")

                print(f"{Fore.GREEN}✓ Updated state.vscdb with {len(updated)} IDs:{Style.RESET_ALL}")
                for update in updated[:5]:  # Show first 5 to avoid spam
                    print(f"     - {update}")
                if len(updated) > 5:
                    print(f"     - ... and {len(updated) - 5} more")
        except Exception as e:
            print(f"{Fore.RED}  -> Error updating {db_file}: {e}{Style.RESET_ALL}")
    else:
        print(f"{Fore.YELLOW}  -> File not found: {db_file}{Style.RESET_ALL}")

    # Update machine ID files (both root and User directories)
    new_machine_id = str(uuid.uuid4())
    updated_files = 0

    for machine_file in config['machine_id_files']:
        print(f"  -> Processing: {machine_file}")
        try:
            old_id = ""
            if os.path.exists(machine_file):
                with open(machine_file, 'r') as f:
                    old_id = f.read().strip()
            else:
                # Create directory if it doesn't exist
                os.makedirs(os.path.dirname(machine_file), exist_ok=True)

            with open(machine_file, 'w') as f:
                f.write(new_machine_id)

            print(f"{Fore.GREEN}✓ Updated machineid file: {os.path.basename(os.path.dirname(machine_file))}/machineid{Style.RESET_ALL}")
            print(f"     - Old: {old_id[:8] if old_id else 'NEW'}...")
            print(f"     - New: {new_machine_id[:8]}...")
            updated_files += 1
        except Exception as e:
            print(f"{Fore.RED}  -> Error updating {machine_file}: {e}{Style.RESET_ALL}")

    if updated_files > 0:
        print(f"{Fore.GREEN}✓ Successfully updated {updated_files} machineid file(s) with same UUID{Style.RESET_ALL}")

def clean_database_file(db_path: str, dry_run: bool = False, _timer: Optional[OperationTimer] = None) -> Dict[str, int]:
    """Clean tracking entries from a single database file."""
    print(f"  -> Processing database: {os.path.basename(db_path)}")

    if not os.path.exists(db_path):
        return {"processed": 0, "errors": 1}

    total_removed = 0
    try:
        write_audit_log("DATABASE_FILE_START", f"Processing database file: {os.path.basename(db_path)}, DryRun: {dry_run}")
        conn = sqlite3.connect(f'file:{db_path}?mode=ro' if dry_run else db_path, uri=True)
        cursor = conn.cursor()

        for pattern in DB_PATTERNS:
            cursor.execute("SELECT COUNT(*) FROM ItemTable WHERE LOWER(key) LIKE LOWER(?)", (pattern,))
            count = cursor.fetchone()[0]
            if count > 0:
                total_removed += count
                if not dry_run:
                    cursor.execute("SELECT key FROM ItemTable WHERE LOWER(key) LIKE LOWER(?) LIMIT 3", (pattern,))
                    examples = [row[0] for row in cursor.fetchall()]
                    if examples:
                        print(f"     - Pattern '{pattern}' matches: {', '.join(examples[:2])}" +
                              (f" (+{count-2} more)" if count > 2 else ""))
                    cursor.execute("DELETE FROM ItemTable WHERE LOWER(key) LIKE LOWER(?)", (pattern,))

        if not dry_run and total_removed > 0:
            conn.commit()
            print(f"     -> Running VACUUM to optimize database...")
            cursor.execute("VACUUM")
            print(f"     -> Database optimized")

        conn.close()

        if total_removed > 0:
            action = "Would remove" if dry_run else "Removed"
            print(f"{Fore.GREEN}✓ {action} {total_removed} entries from {os.path.basename(db_path)}{Style.RESET_ALL}")
            write_audit_log("DATABASE_FILE_CLEANED", f"{os.path.basename(db_path)} - {action} {total_removed} entries, DryRun: {dry_run}")
        else:
            print(f"{Fore.GREEN}✓ No matching entries found in {os.path.basename(db_path)}{Style.RESET_ALL}")
            write_audit_log("DATABASE_FILE_NO_MATCHES", f"{os.path.basename(db_path)} - No matching entries found")

        return {"processed": total_removed, "errors": 0}

    except Exception as e:
        print(f"{Fore.RED}  -> Error cleaning database {os.path.basename(db_path)}: {e}{Style.RESET_ALL}")
        return {"processed": 0, "errors": 1}

def clean_database(config: Dict[str, Any], dry_run: bool = False, timer: Optional[OperationTimer] = None) -> None:
    """Clean tracking entries from all discovered databases."""
    print(f"{Fore.CYAN}--- Database Cleaning Operation ---{Style.RESET_ALL}")

    if timer:
        timer.set_operation("Database Cleaning")
        if timer.check_timeout("database cleaning start"):
            return

    write_audit_log("DATABASE_CLEAN_START", f"Starting database cleaning operation, DryRun: {dry_run}")

    # Discover all database files (like windows.ps1)
    db_files = discover_database_files(config)

    if not db_files:
        print(f"{Fore.YELLOW}  -> No database files found{Style.RESET_ALL}")
        write_audit_log("DATABASE_CLEAN_NO_FILES", "No database files found")
        return

    write_audit_log("DATABASE_DISCOVERY", f"Discovered {len(db_files)} database files")

    total_processed = 0
    total_errors = 0

    # Process each database file
    for db_file in db_files:
        if timer and timer.check_timeout(f"processing {os.path.basename(db_file)}"):
            break

        result = clean_database_file(db_file, dry_run, timer)
        total_processed += result["processed"]
        total_errors += result["errors"]

    # Summary (like windows.ps1)
    action = "Would process" if dry_run else "Processed"
    print(f"{Fore.GREEN}✓ Database cleaning completed. {action}: {total_processed}, Errors: {total_errors}{Style.RESET_ALL}")
    write_audit_log("DATABASE_CLEAN_COMPLETE", f"Database cleaning completed - {action}: {total_processed}, Errors: {total_errors}, DryRun: {dry_run}")

def clean_workspace_storage(config: Dict[str, Any], dry_run: bool = False) -> None:
    """Clean workspace storage directories and their databases."""
    print(f"{Fore.CYAN}--- Workspace Storage Cleaning ---{Style.RESET_ALL}")
    ws_path = config['workspace_storage']
    print(f"  -> Processing workspace storage: {ws_path}")

    if not os.path.isdir(ws_path):
        print(f"{Fore.YELLOW}  -> Workspace storage not found{Style.RESET_ALL}")
        return

    patterns = ['augment', 'context7', 'trial']
    cleaned = 0
    workspaces_scanned = 0

    for ws_dir in os.scandir(ws_path):
        if not ws_dir.is_dir():
            continue

        workspaces_scanned += 1
        print(f"     -> Scanning workspace: {os.path.basename(ws_dir.path)}")

        # Clean workspace database files
        for db_name in ['state.vscdb']:
            db_file = os.path.join(ws_dir.path, db_name)
            if os.path.exists(db_file):
                print(f"        -> Found workspace database: {db_name}")
                clean_database_file(db_file, dry_run)

        # Clean matching files/folders
        try:
            for item in os.listdir(ws_dir.path):
                if any(p in item.lower() for p in patterns):
                    item_path = os.path.join(ws_dir.path, item)
                    cleaned += 1
                    if dry_run:
                        print(f"        -> [Dry Run] Would delete: {item}")
                    else:
                        try:
                            if os.path.isdir(item_path):
                                shutil.rmtree(item_path, ignore_errors=True)
                                print(f"        -> Deleted directory: {item}")
                            else:
                                os.remove(item_path)
                                print(f"        -> Deleted file: {item}")
                        except Exception as e:
                            print(f"        -> Error deleting {item}: {e}")
        except PermissionError:
            print(f"        -> Permission denied")

    action = "Would clean" if dry_run else "Cleaned"
    print(f"{Fore.GREEN}✓ {action} {cleaned} items across {workspaces_scanned} workspace(s){Style.RESET_ALL}")

def clean_caches(config: Dict[str, Any], dry_run: bool = False) -> None:
    """Clean all cache directories."""
    print(f"  -> Processing cache directories...")
    cleared = 0

    for cache_dir in config['cache_dirs']:
        cache_name = os.path.basename(cache_dir)
        print(f"     -> Checking: {cache_name}")
        print(f"        Path: {cache_dir}")

        if os.path.exists(cache_dir):
            if dry_run:
                try:
                    size = sum(os.path.getsize(os.path.join(dirpath, filename))
                              for dirpath, _, filenames in os.walk(cache_dir)
                              for filename in filenames if isinstance(filename, str)) / (1024*1024)
                    print(f"        -> [Dry Run] Would clear ~{size:.1f}MB cache: {cache_name}")
                except:
                    print(f"        -> [Dry Run] Would clear cache: {cache_name}")
            else:
                try:
                    shutil.rmtree(cache_dir, ignore_errors=True)
                    print(f"        -> {Fore.GREEN}✓ Cleared: {cache_name}{Style.RESET_ALL}")
                    cleared += 1
                except Exception as e:
                    print(f"        -> {Fore.RED}✗ Error clearing {cache_name}: {e}{Style.RESET_ALL}")
        else:
            print(f"        -> Not found: {cache_name}")

    action = "Would clear" if dry_run else "Cleared"
    print(f"{Fore.GREEN}✓ {action} {cleared} cache directories{Style.RESET_ALL}")

# --- UI Functions ---

def show_status(_ide_name: str, process_name: str, config: Dict[str, Any]) -> None:
    """Show IDE status and discovered files."""
    status = f"{Fore.YELLOW}RUNNING{Style.RESET_ALL}" if is_ide_running(process_name) else f"{Fore.GREEN}STOPPED{Style.RESET_ALL}"
    print(f"IDE Status: {status}")

    try:
        db_files = discover_database_files(config)
        print(f"Database Files: {Fore.CYAN}{len(db_files)} discovered{Style.RESET_ALL}")

        ws_path = config.get('workspace_storage', '')
        if os.path.isdir(ws_path):
            # Count workspace directories with proper type casting
            workspace_count = 0
            try:
                entries = cast(List[str], os.listdir(ws_path))
                for entry in entries:
                    if os.path.isdir(os.path.join(ws_path, entry)):
                        workspace_count += 1
            except (OSError, PermissionError):
                workspace_count = 0
            print(f"Workspaces: {Fore.CYAN}{workspace_count} found{Style.RESET_ALL}")
        else:
            print(f"Workspaces: {Fore.YELLOW}Not found{Style.RESET_ALL}")
    except Exception:
        print(f"Database Files: {Fore.YELLOW}Scan failed{Style.RESET_ALL}")

def main_menu(ide_name: str, config: Dict[str, str]) -> None:
    """Main menu for selected IDE."""
    write_audit_log("MAIN_MENU_START", f"Starting main menu for {ide_name}")

    while True:
        # Create operation timer for each menu cycle
        timer = OperationTimer()

        os.system('cls' if os.name == 'nt' else 'clear')
        print(f"{Fore.CYAN}{'='*50}")
        print(f"  IDE Reset Tool - {Fore.GREEN}{ide_name}{Style.RESET_ALL}")
        print(f"  Enhanced with windows.ps1 compatibility")
        print(f"{Fore.CYAN}{'='*50}")

        full_config = get_config(config)
        show_status(ide_name, config['process'], full_config)

        print("\nOptions:")
        print("1. Reset Machine IDs (Generate new telemetry identifiers)")
        print("2. Clean Database (Remove Augment/telemetry entries)")
        print("3. Clean Workspace Storage (Clean workspace databases & files)")
        print("4. Clean Caches (Clear all cache directories)")
        print("5. Quick Reset (IDs + Database cleaning)")
        print("6. Full Reset (All operations)")
        print("7. Preview Full Reset (Dry Run - show what would be done)")
        print("8. Back to IDE Selection")
        print(f"\n{Fore.YELLOW}Note: Enhanced with windows.ps1 compatibility{Style.RESET_ALL}")
        print(f"- {len(DB_PATTERNS)} database cleaning patterns")
        print(f"- Comprehensive database file discovery")
        print(f"- VACUUM optimization after cleaning")

        choice = input(f"\nChoice (1-8): ").strip()
        print()

        # Execute operations with enhanced process detection and timeout
        full_config = get_config(config)

        if choice == '1':
            timer.set_operation("Machine ID Reset")
            write_audit_log("OPERATION_START", f"Machine ID Reset - {ide_name}")
            reset_machine_id(full_config)
            write_audit_log("OPERATION_COMPLETE", f"Machine ID Reset - {ide_name}, Duration: {timer.get_elapsed_time():.2f}s")
        elif choice == '2':
            timer.set_operation("Database Cleaning")
            write_audit_log("OPERATION_START", f"Database Cleaning - {ide_name}")
            # Enhanced process detection before database operations
            if not interactive_process_handling(ide_name, config['process'], timer):
                continue
            clean_database(full_config, timer=timer)
            write_audit_log("OPERATION_COMPLETE", f"Database Cleaning - {ide_name}, Duration: {timer.get_elapsed_time():.2f}s")
        elif choice == '3':
            timer.set_operation("Workspace Storage Cleaning")
            write_audit_log("OPERATION_START", f"Workspace Storage Cleaning - {ide_name}")
            if not interactive_process_handling(ide_name, config['process'], timer):
                continue
            clean_workspace_storage(full_config)
            write_audit_log("OPERATION_COMPLETE", f"Workspace Storage Cleaning - {ide_name}, Duration: {timer.get_elapsed_time():.2f}s")
        elif choice == '4':
            timer.set_operation("Cache Cleaning")
            write_audit_log("OPERATION_START", f"Cache Cleaning - {ide_name}")
            if not interactive_process_handling(ide_name, config['process'], timer):
                continue
            print(f"{Fore.CYAN}--- Cache Cleaning ---{Style.RESET_ALL}")
            clean_caches(full_config)
            write_audit_log("OPERATION_COMPLETE", f"Cache Cleaning - {ide_name}, Duration: {timer.get_elapsed_time():.2f}s")
        elif choice == '5':
            timer.set_operation("Quick Reset")
            write_audit_log("OPERATION_START", f"Quick Reset - {ide_name}")
            print(f"{Fore.CYAN}=== Quick Reset ==={Style.RESET_ALL}")
            if not interactive_process_handling(ide_name, config['process'], timer):
                continue
            reset_machine_id(full_config)
            print()
            clean_database(full_config, timer=timer)
            write_audit_log("OPERATION_COMPLETE", f"Quick Reset - {ide_name}, Duration: {timer.get_elapsed_time():.2f}s")
        elif choice == '6':
            timer.set_operation("Full Reset")
            write_audit_log("OPERATION_START", f"Full Reset - {ide_name}")
            print(f"{Fore.CYAN}=== Full Reset ==={Style.RESET_ALL}")
            if not interactive_process_handling(ide_name, config['process'], timer):
                continue
            reset_machine_id(full_config)
            print()
            clean_database(full_config, timer=timer)
            print()
            clean_workspace_storage(full_config)
            print()
            print(f"{Fore.CYAN}--- Cache Cleaning ---{Style.RESET_ALL}")
            clean_caches(full_config)
            write_audit_log("OPERATION_COMPLETE", f"Full Reset - {ide_name}, Duration: {timer.get_elapsed_time():.2f}s")
        elif choice == '7':
            timer.set_operation("Full Reset Preview")
            write_audit_log("OPERATION_START", f"Full Reset Preview (Dry Run) - {ide_name}")
            print(f"{Fore.CYAN}=== PREVIEW: Full Reset (Dry Run) ==={Style.RESET_ALL}")
            print(f"\n{Fore.CYAN}--- Step 1: Machine ID Reset (Preview) ---{Style.RESET_ALL}")
            reset_machine_id(full_config, dry_run=True)
            print()
            clean_database(full_config, dry_run=True, timer=timer)
            print()
            clean_workspace_storage(full_config, dry_run=True)
            print()
            print(f"{Fore.CYAN}--- Cache Cleaning (Preview) ---{Style.RESET_ALL}")
            clean_caches(full_config, dry_run=True)
            print(f"\n{Fore.YELLOW}--- Dry Run Complete - No actual changes made ---{Style.RESET_ALL}")
            write_audit_log("OPERATION_COMPLETE", f"Full Reset Preview (Dry Run) - {ide_name}, Duration: {timer.get_elapsed_time():.2f}s")
        elif choice == '8':
            write_audit_log("MAIN_MENU_EXIT", f"User exited main menu for {ide_name}")
            return
        else:
            print(f"{Fore.RED}Invalid choice{Style.RESET_ALL}")
            time.sleep(1)
            continue

        # Show completion message for valid operations
        print(f"\n{Fore.GREEN}✓ Operation completed!{Style.RESET_ALL}")
        input("Press Enter to continue...")

def select_ide_menu(ide_configs: Dict[str, Dict[str, str]]) -> Optional[str]:
    """IDE selection menu."""
    while True:
        os.system('cls' if os.name == 'nt' else 'clear')
        print(f"{Fore.CYAN}{'='*50}")
        print(f"  IDE Reset Tool v1.0")
        print(f"{Fore.CYAN}{'='*50}")
        print("\nSelect IDE to manage:")

        ide_names: List[str] = list(ide_configs.keys())
        for i, name in enumerate(ide_names, 1):
            print(f"{i}. {name}")
        print("0. Exit")

        choice = input(f"\nChoice (0-{len(ide_names)}): ").strip()

        if choice == '0':
            return None

        try:
            idx = int(choice) - 1
            if 0 <= idx < len(ide_names):
                return ide_names[idx]
        except ValueError:
            pass

        print(f"{Fore.RED}Invalid choice{Style.RESET_ALL}")
        time.sleep(1)

# --- Main ---

def main() -> None:
    try:
        write_audit_log("APPLICATION_START", "IDE Reset Tool started")
        ide_configs = discover_ides()

        if not ide_configs:
            print(f"{Fore.RED}No supported IDEs found. Exiting.{Style.RESET_ALL}")
            input("Press Enter to exit...")
            return

        while True:
            selected_ide = select_ide_menu(ide_configs)
            if selected_ide is None:
                break
            main_menu(selected_ide, ide_configs[selected_ide])

        print("Goodbye!")

    except Exception as e:
        print(f"{Fore.RED}Error: {e}{Style.RESET_ALL}")
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()